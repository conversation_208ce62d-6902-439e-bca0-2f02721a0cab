#!/usr/bin/env python3
"""
Create an RTF version of the project report for maximum compatibility.
RTF files can be opened by Word, Google Docs, LibreOffice, and most text editors.
"""

def create_rtf_report():
    """Create an RTF version of the project report."""
    
    rtf_content = r"""{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}
{\colortbl;\red0\green0\blue0;\red255\green0\blue0;}

\f0\fs24

{\qc\b\fs32 School of Computing and Engineering\par}
{\qc\b\fs28 Final Year Project\par}
\par\par

{\qc Student Name: <PERSON>\par}
{\qc Student ID: 21587131\par}
{\qc Project Title: SMART AI JOB INTERVIEW COACH AND FEEDBACK ASSISTANT USING NLP\par}
{\qc Date: May 2025\par}
{\qc Supervisor Name: Dr. [Supervisor Name]\par}
{\qc Second Marker: Dr. [Second Marker Name]\par}

\page

{\b\fs28 Abstract\par}
\par

This project develops a comprehensive AI-driven web application that simulates realistic job interviews and provides detailed, personalized feedback to users seeking to enhance their interview performance. In today's increasingly competitive job market, effective interview preparation has become a critical determinant of career success, particularly for students and recent graduates entering the workforce. Despite this recognized importance, access to high-quality interview practice opportunities remains limited by cost barriers, scheduling constraints, and the scarcity of qualified professionals available to provide personalized coaching.\par
\par

The AI Job Interview Coach addresses these challenges through an innovative approach combining several advanced technologies. The system leverages natural language processing to enable personalized question generation based on CV analysis, ensuring that interview scenarios are tailored to each user's specific background and skills. This personalization is enhanced through adaptive questioning algorithms that adjust the difficulty and focus of questions based on previous responses. The system provides structured feedback using the STAR (Situation, Task, Action, Result) framework, helping users develop more compelling responses.\par
\par

The application is built with React and TypeScript for the frontend interface, complemented by a Flask and Python backend architecture. The system's intelligence is powered through integration of advanced language models, specifically Gemini 2.0 Flash via the Groq API for cloud-based processing and llama3 via Ollama for local processing, enabling conversational interactions that mimic human interviewer behavior.\par
\par

User testing demonstrated that the application effectively simulates authentic interview scenarios while providing valuable feedback that users could apply to improve their performance. Assessment revealed that users reported increased interview confidence, improvements in response structuring, and enhanced ability to articulate experiences using the STAR methodology. The results support the hypothesis that AI-driven interview simulation represents an effective approach to interview preparation, potentially democratizing access to quality practice opportunities.\par

\page

{\b\fs28 Acknowledgements\par}
\par

I would like to express my sincere gratitude to my supervisor, Dr. [Supervisor Name], for their guidance, support, and valuable feedback throughout this project. Their expertise and insights were instrumental in shaping the direction and implementation of this work.\par
\par

I would also like to thank the faculty members of the School of Computing and Engineering for providing the knowledge and resources necessary to complete this project. I am grateful to my peers who participated in testing the application and provided valuable feedback that helped improve its functionality and user experience.\par
\par

Finally, I would like to thank my family and friends for their unwavering support and encouragement throughout my academic journey.\par

\page

{\b\fs28 Contents\par}
\par

{\b 1. Figures and Tables}\par
\tab 1.1 List of Figures\par
\tab 1.2 List of Tables\par
{\b 2. Introduction}\par
\tab 2.1 Aim and Objectives\par
{\b 3. Literature Review}\par
{\b 4. Research Methodology}\par
{\b 5. Design and Implementation}\par
{\b 6. Result and Analysis}\par
{\b 7. Discussion and Conclusion}\par
{\b 8. References}\par
{\b 9. Appendix}\par

\page

{\b\fs28 1. Figures and Tables\par}
\par

{\b\fs24 1.1 List of Figures\par}
\par

Figure 1 - System Architecture Diagram\par
Figure 2 - User Interface: Home Page\par
Figure 3 - User Interface: Interview Session\par
Figure 4 - User Interface: Feedback Page\par
Figure 5 - CV Analysis Process Flow\par
Figure 6 - Question Generation Algorithm\par
Figure 7 - User Testing Results: Satisfaction Ratings\par
Figure 8 - User Testing Results: Perceived Helpfulness\par
Figure 9 - Results from User Testing\par
Figure 10 - Key Findings\par
Figure 11 - Conclusions and Limitations\par
\par

{\b\fs24 1.2 List of Tables\par}
\par

Table 1 - Comparison of Existing Interview Preparation Tools\par
Table 2 - System Requirements\par
Table 3 - Technologies Used\par
Table 4 - Testing Scenarios and Results\par
Table 5 - User Feedback Summary\par

\page

{\b\fs28 2. Introduction\par}
\par

In today's competitive job market, interview performance is crucial for career outcomes, particularly challenging for students and recent graduates with limited experience. The National Association of Colleges and Employers (2024) reports that 73% of employers consider interview performance the most important hiring factor, yet over 60% of candidates feel significantly underprepared.\par
\par

Traditional interview preparation methods have notable limitations: career counselor sessions face availability constraints, peer practice lacks structured feedback, and professional coaching services are prohibitively expensive (£75-£200/hour). Research by the Society for Human Resource Management (2023) shows candidates with structured interview practice are 37% more likely to receive job offers and negotiate 7-10% higher starting salaries.\par
\par

Recent advancements in AI and NLP technologies have created opportunities for developing systems that simulate conversations and provide personalized feedback at scale. The AI Job Interview Coach leverages these technologies to create an accessible, personalized interview preparation tool that addresses traditional method limitations through:\par
\par

1. CV-based personalization: Questions tailored to the user's background and experience\par
2. Adaptive questioning: Dynamic adjustment based on previous responses\par
3. STAR-based feedback: Structured evaluation of response components\par
\par

The system was implemented using React/TypeScript (frontend) and Flask/Python (backend), with AI integration through Groq API (Gemini 2.0 Flash) for cloud processing and Ollama (llama3) for local processing.\par

\page

{\b\fs28 [Additional sections would continue here...]\par}
\par

{\i Note: This RTF file contains the beginning sections of your report. The complete content from your FINAL_PROJECT_REPORT.md file should be added to create the full document. This format ensures maximum compatibility across different word processors.}\par

}"""
    
    # Write RTF file
    with open('FINAL_PROJECT_REPORT.rtf', 'w', encoding='utf-8') as f:
        f.write(rtf_content)
    
    print("✅ RTF document created: FINAL_PROJECT_REPORT.rtf")
    print("📝 This RTF file can be opened in Word, Google Docs, LibreOffice, and most text editors")
    return True

if __name__ == "__main__":
    success = create_rtf_report()
    if success:
        print("✅ RTF document created successfully!")
    else:
        print("❌ Failed to create RTF document.")
