# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Model files
models/*.pt
models/*.bin
models/*.pth
models/*.onnx

# Data
data/raw/*
data/processed/*
!data/raw/.gitkeep
!data/processed/.gitkeep

# Logs
*.log
logs/

# Project specific
*.pt
*.pth
*.bin
*.log
data/processed/
data/raw/

# Local development settings
.env
.env.local

# Testing
.pytest_cache/
.coverage
htmlcov/
coverage.xml

# Additional temporary files
*.bak
*.tmp
*.temp 
node_modules
