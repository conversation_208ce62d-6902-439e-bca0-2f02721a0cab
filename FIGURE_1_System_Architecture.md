# Figure 1: System Architecture Diagram

## Mermaid Diagram Code

```mermaid
graph TD
    A[User Interface<br/>React + TypeScript] --> B[Flask Backend<br/>Python API]
    B --> C[AI Services]
    B --> D[SQLite Database]

    C --> E[Groq API<br/>Gemini 2.0 Flash]
    C --> F[Ollama<br/>llama3 Local]

    subgraph "Key Features"
        G[CV Analysis]
        H[Question Generation]
        I[STAR Feedback]
        J[Session Management]
    end

    B --> G
    B --> H
    B --> I
    B --> J

    classDef frontend fill:#e1f5fe,stroke:#1976d2,stroke-width:2px
    classDef backend fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef ai fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef data fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef features fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class A frontend
    class B backend
    class C,E,F ai
    class D data
    class G,H,I,<PERSON> features
```

## Key Architectural Decisions:

1. **Separation of Concerns**: Clear separation between presentation, application, AI services, and data layers
2. **Dual AI Provider**: Primary cloud service with local fallback for reliability and privacy
3. **RESTful API**: Standard HTTP/REST communication between frontend and backend
4. **Component-Based Frontend**: React components for reusability and maintainability
5. **Service-Oriented Backend**: Modular services for different functionalities
6. **Lightweight Database**: SQLite for simplicity, easily upgradeable to PostgreSQL
7. **Adapter Pattern**: Common interface for different AI providers
8. **Stateless Design**: Backend services are stateless for scalability
