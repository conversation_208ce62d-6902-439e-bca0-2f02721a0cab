#!/usr/bin/env python3
"""
Convert the project report from markdown to Word format
with proper formatting and structure.
"""

try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.style import WD_STYLE_TYPE
    from docx.oxml.shared import OxmlElement, qn
except ImportError:
    print("python-docx not installed. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH

import os

def read_markdown_content():
    """Read the content from the markdown file."""
    try:
        with open('FINAL_PROJECT_REPORT.md', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print("FINAL_PROJECT_REPORT.md not found!")
        return None

def create_word_report():
    """Create a properly formatted Word document from the project report."""

    # Read markdown content
    markdown_content = read_markdown_content()
    if not markdown_content:
        return

    # Create a new document
    doc = Document()

    # Title page
    title = doc.add_heading('School of Computing and Engineering', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    subtitle = doc.add_heading('Final Year Project', 1)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

    doc.add_paragraph()

    # Project details
    details = [
        "Student Name: Mohammed Zeeshan",
        "Student ID: 21587131",
        "Project Title: SMART AI JOB INTERVIEW COACH AND FEEDBACK ASSISTANT USING NLP",
        "Date: May 2025",
        "Supervisor Name: Dr. [Supervisor Name]",
        "Second Marker: Dr. [Second Marker Name]"
    ]

    for detail in details:
        p = doc.add_paragraph(detail)
        p.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Page break
    doc.add_page_break()

    # Abstract
    doc.add_heading('Abstract', 1)
    abstract_text = """This project develops a comprehensive AI-driven web application that simulates realistic job interviews and provides detailed, personalized feedback to users seeking to enhance their interview performance. In today's increasingly competitive job market, effective interview preparation has become a critical determinant of career success, particularly for students and recent graduates entering the workforce.

The AI Job Interview Coach addresses these challenges through an innovative approach combining several advanced technologies. The system leverages natural language processing to enable personalized question generation based on CV analysis, ensuring that interview scenarios are tailored to each user's specific background and skills. This personalization is enhanced through adaptive questioning algorithms that adjust the difficulty and focus of questions based on previous responses. The system provides structured feedback using the STAR (Situation, Task, Action, Result) framework, helping users develop more compelling responses.

The application is built with React and TypeScript for the frontend interface, complemented by a Flask and Python backend architecture. The system's intelligence is powered through integration of advanced language models, specifically Gemini 2.0 Flash via the Groq API for cloud-based processing and llama3 via Ollama for local processing, enabling conversational interactions that mimic human interviewer behavior.

User testing demonstrated that the application effectively simulates authentic interview scenarios while providing valuable feedback that users could apply to improve their performance. Assessment revealed that users reported increased interview confidence, improvements in response structuring, and enhanced ability to articulate experiences using the STAR methodology. The results support the hypothesis that AI-driven interview simulation represents an effective approach to interview preparation, potentially democratizing access to quality practice opportunities."""

    doc.add_paragraph(abstract_text)

    # Acknowledgements
    doc.add_page_break()
    doc.add_heading('Acknowledgements', 1)
    ack_text = """I would like to express my sincere gratitude to my supervisor, Dr. [Supervisor Name], for their guidance, support, and valuable feedback throughout this project. Their expertise and insights were instrumental in shaping the direction and implementation of this work.

I would also like to thank the faculty members of the School of Computing and Engineering for providing the knowledge and resources necessary to complete this project. I am grateful to my peers who participated in testing the application and provided valuable feedback that helped improve its functionality and user experience.

Finally, I would like to thank my family and friends for their unwavering support and encouragement throughout my academic journey."""

    doc.add_paragraph(ack_text)

    # Table of Contents
    doc.add_page_break()
    doc.add_heading('Table of Contents', 1)

    toc_items = [
        "1. Introduction",
        "2. Literature Review",
        "3. Research Methodology",
        "4. Design and Implementation",
        "5. Results and Analysis",
        "6. Discussion and Conclusion",
        "7. References",
        "8. Appendix"
    ]

    for item in toc_items:
        doc.add_paragraph(item)

    # Save the document
    doc.save('AI_Job_Interview_Coach_Final_Report.docx')
    print("Word document created successfully: AI_Job_Interview_Coach_Final_Report.docx")
    return True

if __name__ == "__main__":
    success = create_word_report()
    if success:
        print("✅ Word document created successfully!")
    else:
        print("❌ Failed to create Word document.")
