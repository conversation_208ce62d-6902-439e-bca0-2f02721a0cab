# Figure 5: CV Analysis Process Flow

## Mermaid Diagram Code

```mermaid
flowchart TD
    A[CV Upload] --> B{Valid File?}
    B -->|No| C[Error Message]
    B -->|Yes| D[Extract Text]
    D --> E[Identify Sections]
    E --> F[Extract Information]
    F --> G[Create Profile]
    G --> H[Save to Database]

    subgraph "Extracted Data"
        I[Personal Info]
        J[Education]
        K[Experience]
        L[Skills]
    end

    F --> I
    F --> J
    F --> K
    F --> L

    classDef start fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef data fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px

    class A start
    class D,E,F,G,H process
    class B decision
    class C error
    class I,<PERSON>,<PERSON>,<PERSON> data
```

## Process Description

### 1. File Upload & Validation
- User uploads CV document in .docx format
- System validates file format, size, and security
- Invalid files are rejected with appropriate error messages

### 2. Document Processing
- Parse DOCX structure using python-docx library
- Extract raw text while preserving formatting
- Clean and normalize text data

### 3. Section Identification
- Use pattern matching to identify CV sections
- Recognize standard headings and structures
- Handle variations in CV formatting

### 4. Information Extraction
- Extract structured data from each section
- Use NLP techniques for entity recognition
- Validate and categorize extracted information

### 5. Data Processing & Analysis
- Map skills to standard categories
- Calculate experience levels and progression
- Classify industry and role preferences

### 6. Profile Generation
- Create comprehensive user profile
- Generate summary and insights
- Store in database for future use

### Error Handling
- Comprehensive validation at each step
- Graceful handling of malformed documents
- Clear error messages for user feedback

### Output
- Structured JSON profile data
- Extracted information summary
- Analysis results and recommendations
