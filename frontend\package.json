{"name": "temp-project", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.125", "@mui/material": "^5.17.1", "@mui/system": "^5.17.1", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "ajv": "^6.12.6", "ajv-keywords": "^3.5.2", "axios": "^1.8.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "react-scripts": "^5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "set NODE_OPTIONS=--max-old-space-size=4096 && set PORT=3000 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/axios": "^0.9.36", "@types/history": "^5.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-router-dom": "^5.3.3", "@types/scheduler": "^0.26.0", "rimraf": "^6.0.1"}, "proxy": "http://localhost:5000"}