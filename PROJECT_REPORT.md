# School of Computing and Engineering
# Final Year Project

**Student Name:** [Your Name]
**Student ID:** [Your ID]
**Project Title:** SMART AI JOB INTERVIEW COACH AND FEEDBACK ASSISTANT USING NLP
**Date:** May 19, 2024
**Supervisor Name:** [Supervisor Name]
**Second Marker:** [Second Marker Name]

## Abstract

This project investigates the development of an AI-driven web application designed to simulate job interviews and provide personalized feedback to users. With the increasing competitiveness of the job market, effective interview preparation has become crucial for job seekers, particularly for students and recent graduates. Previous research has shown that practice interviews significantly improve performance, but access to quality interview practice remains limited due to cost and availability constraints.

The AI Job Interview Coach addresses this gap by providing an accessible platform for interview simulation and feedback. The system was hypothesized to improve interview preparation through personalized question generation based on CV analysis, adaptive questioning, and structured feedback using the STAR (Situation, Task, Action, Result) framework.

The application was developed using a full-stack approach with React and TypeScript for the frontend, Flask and Python for the backend, and integration with advanced language models (Gemini 2.0 Flash via Groq API and llama3 via Ollama) for AI-driven interactions. The system includes CV parsing capabilities, user authentication, session management, and comprehensive feedback generation.

Testing with a sample of users demonstrated that the AI Job Interview Coach effectively simulates realistic interview scenarios and provides valuable feedback. Users reported increased confidence and improved response structuring after using the application. The results support the hypothesis that AI-driven interview simulation can enhance interview preparation, though further research is needed to measure long-term impact on actual job interview outcomes.

## Acknowledgements

I would like to express my sincere gratitude to my supervisor, [Supervisor Name], for their guidance, support, and valuable feedback throughout this project. Their expertise and insights were instrumental in shaping the direction and implementation of this work.

I would also like to thank the faculty members of the School of Computing and Engineering for providing the knowledge and resources necessary to complete this project. Special thanks to [Any specific faculty members who helped] for their assistance with [specific aspects they helped with].

I am grateful to my peers who participated in testing the application and provided valuable feedback that helped improve its functionality and user experience.

Finally, I would like to thank my family and friends for their unwavering support and encouragement throughout my academic journey.

## Contents

1. [Figures and Tables](#figures-and-tables)
   1. [List of Figures](#list-of-figures)
   2. [List of Tables](#list-of-tables)
2. [Introduction](#introduction)
   1. [Aim and Objectives](#aim-and-objectives)
      1. [Aim](#aim)
      2. [Objectives](#objectives)
3. [Literature Review](#literature-review)
4. [Research Methodology](#research-methodology)
5. [Design and Implementation](#design-and-implementation)
6. [Result and Analysis](#result-and-analysis)
7. [Discussion and Conclusion](#discussion-and-conclusion)
8. [References](#references)
9. [Appendix](#appendix)

## 1. Figures and Tables

### 1.1 List of Figures

Figure 1 - System Architecture Diagram
Figure 2 - User Interface: Home Page
Figure 3 - User Interface: Interview Session
Figure 4 - User Interface: Feedback Page
Figure 5 - CV Analysis Process Flow
Figure 6 - Question Generation Algorithm
Figure 7 - User Testing Results: Satisfaction Ratings
Figure 8 - User Testing Results: Perceived Helpfulness
Figure 9 - Results from User Testing
Figure 10 - Key Findings
Figure 11 - Conclusions and Limitations

### 1.2 List of Tables

Table 1 - Comparison of Existing Interview Preparation Tools
Table 2 - System Requirements
Table 3 - Technologies Used
Table 4 - Testing Scenarios and Results
Table 5 - User Feedback Summary

## 2. Introduction

In today's competitive job market, interview performance plays a crucial role in determining career outcomes. For students and recent graduates, the interview process can be particularly challenging due to limited professional experience and interview practice. According to a survey by the National Association of Colleges and Employers (2022), 73% of employers consider interview performance as the most important factor in hiring decisions, yet many candidates report feeling underprepared for interviews.

Traditional interview preparation methods include mock interviews with career counselors, peers, or professional coaches. While effective, these methods have limitations in terms of accessibility, cost, and scheduling flexibility. Additionally, feedback from human coaches may vary in quality and consistency.

The emergence of artificial intelligence and natural language processing technologies has created new opportunities for developing intelligent systems that can simulate human-like conversations and provide personalized feedback. These technologies have been successfully applied in various educational contexts, suggesting potential for application in interview preparation.

The AI Job Interview Coach project aims to leverage these technologies to create an accessible, personalized, and effective interview preparation tool. By combining CV analysis, adaptive questioning, and structured feedback based on the STAR framework, the system seeks to provide a comprehensive interview preparation experience that addresses the limitations of traditional methods.

This project was inspired by existing applications like Yoodli, but with a focus on creating a more personalized experience through CV integration and adaptive questioning. The implementation uses React and TypeScript for the frontend, Flask and Python for the backend, and integrates with advanced AI models through the Groq API (using Gemini 2.0 Flash) and Ollama (using llama3) for local processing. The system includes features such as user authentication, CV parsing and analysis, interview simulation with AI-driven questioning, session management, and comprehensive feedback generation.

A key innovation of this project is the integration of CV analysis with interview simulation, allowing the system to generate questions that are specifically relevant to the user's background and experience. This personalization, combined with adaptive questioning and structured feedback, creates a more effective and engaging interview preparation experience than generic practice tools.

This report details the development, implementation, and evaluation of the AI Job Interview Coach, with particular focus on the challenges encountered and solutions developed during the implementation process, including fixing API endpoint issues, resolving DOM nesting errors, improving the light/dark mode toggle functionality, and enhancing the chatbot experience to prevent question repetition and provide proper session conclusion.

### 2.1 Aim and Objectives

#### 2.1.1 Aim

The aim of this project is to develop an AI-driven web application that simulates job interviews and provides personalized feedback to help users improve their interview skills and confidence.

#### 2.1.2 Objectives

1. **Research and Analysis (January 2024 - February 2024)**
   - Conduct a comprehensive literature review on interview preparation techniques, AI-driven conversation systems, and feedback mechanisms
   - Analyze existing interview preparation tools like Yoodli to identify strengths and limitations
   - Define system requirements based on user needs and technological feasibility
   - Evaluate AI integration options including Groq API and Ollama

2. **Design and Architecture (February 2024 - March 2024)**
   - Design the system architecture for the web application
   - Create wireframes and user interface designs inspired by ChatGPT but with unique elements
   - Define the data models and database schema for user profiles and sessions
   - Design the AI integration strategy for question generation and response analysis

3. **Implementation (March 2024 - April 2024)**
   - Develop the frontend using React, TypeScript, and Material-UI
   - Implement the backend using Flask and Python
   - Integrate with AI services (Groq API with Gemini 2.0 Flash and Ollama with llama3)
   - Implement CV parsing and analysis functionality using the docx library
   - Develop user authentication and session management
   - Create the feedback generation system using the STAR framework

4. **Bug Fixing and Enhancement (April 2024 - May 2024)**
   - Fix API endpoint issues with `/api/sessions`, `/api/clear-profile`, and `/api/save-profile`
   - Resolve DOM nesting error in the Logo component
   - Fix light/dark mode toggle functionality
   - Enhance the chatbot to prevent question repetition
   - Improve session ending logic after 15 questions
   - Implement proper CV data persistence between sessions

5. **Testing and Finalization (May 2024)**
   - Conduct functional testing of all system components
   - Perform user testing with university peers
   - Collect and analyze feedback
   - Make final adjustments based on testing results
   - Clean up and organize file structure
   - Prepare for project presentation on May 21, 2024
