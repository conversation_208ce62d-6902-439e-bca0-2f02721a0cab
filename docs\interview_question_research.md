# Research on Interview Question Sequencing and Technical Questions

## 1. Interview Question Sequencing

### Common Approaches in the Literature

#### 1.1 The Funnel Approach
The funnel approach starts with broad, open-ended questions and gradually narrows to more specific, detailed questions. This is widely used in behavioral interviews.

**Research Support:**
- <PERSON> (1998) found that structured interviews with a progressive sequence of questions have higher predictive validity for job performance.
- <PERSON><PERSON> et al. (1997) demonstrated that a systematic progression from general to specific questions helps establish rapport while gathering detailed information.

**Implementation:**
- Begin with introductory questions about background and interests
- Move to behavioral questions about past experiences
- Progress to situational questions about hypothetical scenarios
- End with specific technical questions relevant to the role

#### 1.2 The STAR Method Progression
Many interview systems build on the STAR (Situation, Task, Action, Result) framework by sequencing questions to elicit complete STAR responses.

**Research Support:**
- <PERSON><PERSON> & <PERSON> (1999) found that structured behavioral interviews using the STAR method had higher validity than unstructured interviews.
- <PERSON><PERSON><PERSON> & <PERSON>hmitt (1995) demonstrated that questions designed to elicit complete STAR responses provide better predictive validity.

**Implementation:**
- Initial questions focus on identifying situations
- Follow-up questions probe for specific tasks and actions
- Final questions in a sequence ask about results and reflections

#### 1.3 Adaptive Questioning
Adaptive questioning adjusts the difficulty and focus of questions based on previous responses.

**Research Support:**
- <PERSON><PERSON><PERSON> et al. (2014) found that adaptive questioning improves the quality of information gathered in interviews.
- Cortina et al. (2000) showed that adjusting question difficulty based on candidate responses provides better discrimination between candidates.

**Implementation:**
- If a candidate gives a strong answer, follow with a more challenging question
- If a candidate struggles, provide a simpler follow-up question
- Use previous answers to inform the topic of subsequent questions

### 2. Technical Question Sequencing

#### 2.1 Knowledge-Skill-Application Progression
Technical interviews often follow a progression from knowledge to application.

**Research Support:**
- Huffcutt et al. (2001) found that technical interviews that progress from knowledge to application have higher validity.
- McFarland et al. (2020) demonstrated that this progression helps assess both theoretical understanding and practical skills.

**Implementation:**
- Begin with questions about technical knowledge and concepts
- Progress to questions about how the candidate has applied these concepts
- End with problem-solving scenarios that require applying knowledge in new contexts

#### 2.2 Domain-Specific Frameworks
Different technical domains have developed specialized interview frameworks.

**Research Support:**
- For software engineering: Behroozi et al. (2019) analyzed technical interviews at major tech companies and found common patterns in question sequencing.
- For data science: Gonzalez et al. (2020) identified effective question sequences for assessing analytical skills.

**Implementation:**
- Software Engineering: Concept questions → Coding problems → System design
- Data Science: Statistical knowledge → Data manipulation → Model building → Interpretation
- Project Management: Methodology knowledge → Planning scenarios → Risk management → Stakeholder handling

## 3. Personalization Based on CV/Profile

### 3.1 Skill-Based Adaptation
Questions can be sequenced to focus on areas mentioned in the candidate's CV.

**Research Support:**
- Breaugh (2009) found that interviews tailored to a candidate's background yield more valid assessments.
- Dipboye et al. (2001) demonstrated that personalized question sequences increase the accuracy of hiring decisions.

**Implementation:**
- Identify key skills and experiences from the CV
- Sequence questions to explore these areas in depth
- Include questions that connect different experiences mentioned in the CV

### 3.2 Experience-Level Adaptation
Question difficulty and focus can be adjusted based on the candidate's experience level.

**Research Support:**
- Posthuma et al. (2002) found that different question sequences are appropriate for different experience levels.
- Roth et al. (2005) demonstrated that adapting question difficulty to experience level improves predictive validity.

**Implementation:**
- Entry-level: Focus on educational background, theoretical knowledge, and potential
- Mid-level: Emphasize practical experience and specific accomplishments
- Senior-level: Focus on strategic thinking, leadership, and complex problem-solving

## 4. Safe First Questions

### 4.1 Characteristics of Effective Opening Questions
Research indicates that effective first questions share certain characteristics.

**Research Support:**
- Chapman & Zweig (2005) found that interviews that begin with rapport-building questions lead to more accurate assessments.
- Huffcutt & Youngcourt (2007) demonstrated that non-threatening opening questions reduce candidate anxiety and improve response quality.

**Implementation:**
- Open-ended: Allow candidates to share information freely
- Non-threatening: Focus on positive aspects of experience
- Relevant: Connect to the role but don't require specialized knowledge
- Engaging: Encourage thoughtful reflection rather than memorized responses

### 4.2 Recommended First Questions
Based on research, these opening questions are considered "safe" for most candidates:

1. "Could you tell me a bit about your professional background and what interests you about this role?"
   - Allows candidates to highlight relevant experiences
   - Doesn't assume specific experience or knowledge
   - Gives insight into motivation and fit

2. "What aspects of your education or experience do you think are most relevant to this position?"
   - Lets candidates focus on their strengths
   - Works for candidates with diverse backgrounds
   - Provides insight into how candidates understand the role

3. "Could you describe a project or accomplishment you're particularly proud of?"
   - Positive framing reduces anxiety
   - Works for candidates at all experience levels
   - Provides insight into values and priorities

## 5. References

1. Behroozi, M., Shirolkar, A., Barik, T., & Parnin, C. (2019). Debugging hiring: What went right and what went wrong in the technical interview process. In 2019 IEEE/ACM 41st International Conference on Software Engineering: Software Engineering Education and Training (ICSE-SEET) (pp. 1-10).

2. Breaugh, J. A. (2009). The use of biodata for employee selection: Past research and future directions. Human Resource Management Review, 19(3), 219-231.

3. Campion, M. A., Palmer, D. K., & Campion, J. E. (1997). A review of structure in the selection interview. Personnel Psychology, 50(3), 655-702.

4. Chapman, D. S., & Zweig, D. I. (2005). Developing a nomological network for interview structure: Antecedents and consequences of the structured selection interview. Personnel Psychology, 58(3), 673-702.

5. Cortina, J. M., Goldstein, N. B., Payne, S. C., Davison, H. K., & Gilliland, S. W. (2000). The incremental validity of interview scores over and above cognitive ability and conscientiousness scores. Personnel Psychology, 53(2), 325-351.

6. Dipboye, R. L., Macan, T., & Shahani-Denning, C. (2001). The selection interview from the interviewer and applicant perspectives: Can't have one without the other. International Review of Industrial and Organizational Psychology, 17, 35-76.

7. Huffcutt, A. I., & Youngcourt, S. S. (2007). Employment interviews. In D. L. Whetzel & G. R. Wheaton (Eds.), Applied measurement: Industrial psychology in human resources management (pp. 181-199). Lawrence Erlbaum Associates.

8. Huffcutt, A. I., Conway, J. M., Roth, P. L., & Stone, N. J. (2001). Identification and meta-analytic assessment of psychological constructs measured in employment interviews. Journal of Applied Psychology, 86(5), 897-913.

9. Latham, G. P., & Sue-Chan, C. (1999). A meta-analysis of the situational interview: An enumerative review of reasons for its validity. Canadian Psychology, 40(1), 56-67.

10. Levashina, J., Hartwell, C. J., Morgeson, F. P., & Campion, M. A. (2014). The structured employment interview: Narrative and quantitative review of the research literature. Personnel Psychology, 67(1), 241-293.

11. Posthuma, R. A., Morgeson, F. P., & Campion, M. A. (2002). Beyond employment interview validity: A comprehensive narrative review of recent research and trends over time. Personnel Psychology, 55(1), 1-81.

12. Pulakos, E. D., & Schmitt, N. (1995). Experience-based and situational interview questions: Studies of validity. Personnel Psychology, 48(2), 289-308.

13. Roth, P. L., Van Iddekinge, C. H., Huffcutt, A. I., Eidson Jr, C. E., & Schmit, M. J. (2005). Personality saturation in structured interviews. International Journal of Selection and Assessment, 13(4), 261-273.

14. Schmidt, F. L., & Hunter, J. E. (1998). The validity and utility of selection methods in personnel psychology: Practical and theoretical implications of 85 years of research findings. Psychological Bulletin, 124(2), 262-274.
