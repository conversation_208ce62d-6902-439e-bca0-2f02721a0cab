# Application Settings
FLASK_APP=app.backend.app:create_app()
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///instance/interview_coach.sqlite

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000

# NLP Model Settings
SPACY_MODEL=en_core_web_sm
MAX_SUMMARY_LENGTH=1024
MIN_PROFESSIONAL_TONE=0.6
MIN_CONFIDENCE_TONE=0.5
MAX_SENTENCE_LENGTH=25
MAX_READABILITY_SCORE=30
MIN_ENTITY_DENSITY=0.1

# Redis Configuration (optional)
# REDIS_URL=localhost:6379

# Session Settings
SESSION_TYPE=filesystem
SESSION_PERMANENT=True
PERMANENT_SESSION_LIFETIME=3600

# API Configuration
# Get your Groq API key from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here
GROQ_API_BASE=https://api.groq.com/openai/v1
GROQ_DEFAULT_MODEL=llama3-8b-8192

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-here
