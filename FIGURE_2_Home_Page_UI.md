# Figure 2: User Interface - Home Page

## Mermaid Diagram Code

```mermaid
graph TD
    subgraph "Home Page Layout"
        A[Header: AI Job Interview Coach<br/>Navigation & Login]
        B[Hero Section<br/>🎯 Master Your Next Interview<br/>AI-powered practice with CV analysis]
        C[Key Features Grid<br/>📄 CV Analysis | 🤖 AI Questions | ⭐ STAR Feedback]
        D[How It Works<br/>1️⃣ Upload CV → 2️⃣ AI Analysis → 3️⃣ Practice → 4️⃣ Improve]
        E[Call to Action<br/>[Start Practicing Now] [Sign In]]
        F[Footer<br/>© 2025 AI Job Interview Coach]
    end

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F

    classDef header fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef content fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef cta fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef footer fill:#fff3e0,stroke:#ff9800,stroke-width:2px

    class A header
    class B,C,D content
    class E cta
    class F footer
```

## Design Specifications

### Color Scheme
- **Primary**: Blue (#1976d2) for main actions and highlights
- **Secondary**: Green (#4caf50) for success states and positive feedback
- **Background**: White (#ffffff) in light mode, Dark gray (#121212) in dark mode
- **Text**: Dark gray (#333333) in light mode, Light gray (#ffffff) in dark mode

### Typography
- **Headings**: Roboto, 24-32px, Bold
- **Body Text**: Roboto, 16px, Regular
- **Buttons**: Roboto, 14px, Medium

### Interactive Elements
- **Buttons**: Rounded corners (8px), hover effects with color transitions
- **Cards**: Subtle shadows, hover elevation effects
- **Navigation**: Smooth transitions between sections

### Responsive Design
- **Desktop**: Full-width layout with side-by-side feature cards
- **Tablet**: Stacked layout with 2-column feature grid
- **Mobile**: Single-column layout with full-width elements

## User Flow
1. User lands on home page
2. Reads feature overview and value proposition
3. Clicks "Get Started" or "Start Practicing Now"
4. Redirected to registration/login if not authenticated
5. After authentication, directed to user profile/dashboard page

## Accessibility Features
- High contrast ratios for text readability
- Keyboard navigation support
- Screen reader compatible with proper ARIA labels
- Focus indicators for interactive elements
- Alternative text for icons and images
