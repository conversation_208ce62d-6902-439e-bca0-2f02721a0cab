# Figure 6: Question Generation Algorithm

## Mermaid Diagram Code

```mermaid
flowchart TD
    A[Load User Profile] --> B[Analyze Context]
    B --> C[Select Question Type]
    C --> D{Check Performance}
    D -->|Weak| E[Easier Questions]
    D -->|Good| F[Same Level]
    D -->|Strong| G[Harder Questions]

    E --> H[Personalize Question]
    F --> H
    G --> H

    H --> I[Generate with AI]
    I --> J{Quality OK?}
    J -->|No| I
    J -->|Yes| K[Present to User]
    K --> L{Session Complete?}
    L -->|No| B
    L -->|Yes| M[End Session]

    classDef start fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef difficulty fill:#ffebee,stroke:#f44336,stroke-width:2px

    class A,M start
    class B,C,H,K process
    class D,J,L decision
    class I ai
    class E,F,G difficulty
```

## Algorithm Description

### 1. Session Initialization
- Load user profile and CV data
- Initialize conversation context
- Set session parameters and goals

### 2. Context Analysis
- Analyze CV data for relevant information
- Assess user experience level
- Review previous responses and performance
- Consider question history to avoid repetition

### 3. Question Type Selection
- **Behavioral**: Past behavior and experiences
- **Technical**: Skills and knowledge assessment
- **Situational**: Hypothetical scenario responses
- **Experience-based**: Specific work experiences
- **Skills-focused**: Competency demonstrations

### 4. Adaptive Difficulty Logic
- **Performance Assessment**: Analyze previous response quality
- **Dynamic Adjustment**: Modify difficulty based on performance
- **Progressive Challenge**: Gradually increase complexity
- **Confidence Building**: Support struggling users

### 5. Personalization Process
- Insert specific CV details (companies, projects, technologies)
- Add contextual information from user background
- Customize examples to match experience level
- Reference specific achievements and skills

### 6. AI Integration
- **Primary Service**: Groq API with Gemini 2.0 Flash
- **Fallback Service**: Local Ollama with llama3
- **Error Handling**: Robust retry mechanisms
- **Quality Control**: Response validation and filtering

### 7. Question Validation
- Format checking and consistency
- Content relevance verification
- Uniqueness assurance (no duplicates)
- Appropriateness for user level

### 8. Continuous Learning
- Track question effectiveness
- Monitor user engagement
- Adjust algorithms based on feedback
- Improve personalization over time

## Key Features

### Adaptive Intelligence
- Questions become more challenging as user improves
- Difficulty adjusts based on real-time performance
- Personalized learning path for each user

### Quality Assurance
- Multi-layer validation process
- Automatic regeneration for poor quality questions
- Professional tone and clarity standards

### Context Awareness
- Deep integration with CV data
- Conversation history consideration
- Progressive question building

### Reliability
- Dual AI provider strategy
- Comprehensive error handling
- Graceful degradation capabilities
