# Figure 3: User Interface - Interview Session

## Mermaid Diagram Code

```mermaid
graph TD
    subgraph "Interview Session Layout"
        A[Header: Progress Bar<br/>Question 7/15 - 47% Complete]
        B[Chat Area<br/>🤖 AI: Question about your experience<br/>👤 You: Response using STAR method]
        C[Input Area<br/>💡 STAR Tip | Text Box | [Send] Button]
        D[Sidebar<br/>📊 Stats | 📝 Notes | 🎯 Focus Areas]
    end

    A --> B
    B --> C
    B --> D

    subgraph "Key Features"
        E[Real-time Chat]
        F[STAR Guidance]
        G[Progress Tracking]
        H[Session Controls]
    end

    B --> E
    C --> F
    A --> G
    D --> H

    classDef header fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef chat fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef input fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef sidebar fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef feature fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class A header
    class B chat
    class C input
    class D sidebar
    class E,F,G,H feature
```

## Interactive Features

### Message Types
1. **AI Questions**: Blue background, robot icon
2. **User Responses**: Gray background, user icon
3. **System Messages**: Light background, info icon
4. **Typing Indicators**: Animated dots showing AI is processing

### Real-time Features
- **Live typing indicators** when AI is generating responses
- **Message timestamps** for each exchange
- **Read receipts** showing message delivery status
- **Auto-scroll** to latest messages

### STAR Framework Helper
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           STAR Framework Guide                             │
├─────────────────────────────────────────────────────────────────────────────┤
│  📍 Situation: Describe the context and background                         │
│  🎯 Task: Explain what needed to be accomplished                           │
│  ⚡ Action: Detail the specific steps you took                             │
│  🏆 Result: Share the outcomes and what you learned                        │
│                                                                             │
│  [Show Example] [Hide Guide]                                               │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Session Controls
- **Pause/Resume**: Temporarily stop the interview
- **Save Progress**: Save current state to continue later
- **End Session**: Complete the interview and view feedback
- **Emergency Exit**: Quick exit with auto-save

## Responsive Design

### Desktop (1200px+)
- Full sidebar visible
- Wide chat area with comfortable message spacing
- Multi-line input field with formatting options

### Tablet (768px - 1199px)
- Collapsible sidebar (hidden by default)
- Optimized chat area width
- Touch-friendly input controls

### Mobile (< 768px)
- No sidebar (accessible via menu)
- Full-width chat interface
- Mobile keyboard optimization
- Swipe gestures for navigation

## Accessibility Features

### Keyboard Navigation
- Tab through all interactive elements
- Enter to send messages
- Escape to access menu
- Arrow keys for message history

### Screen Reader Support
- Proper ARIA labels for all elements
- Live regions for new messages
- Descriptive text for icons and buttons
- Structured heading hierarchy

### Visual Accessibility
- High contrast mode support
- Scalable text (up to 200%)
- Focus indicators on all interactive elements
- Color-blind friendly design

## Technical Implementation

### State Management
- Real-time message synchronization
- Session persistence across page refreshes
- Optimistic UI updates for better UX
- Error handling with retry mechanisms

### Performance Optimization
- Message virtualization for long conversations
- Lazy loading of message history
- Efficient re-rendering with React.memo
- Debounced typing indicators

### Security Features
- Input sanitization for all user messages
- XSS protection for message content
- Secure session token management
- Rate limiting for message sending

## User Experience Flow

1. **Session Start**: Welcome message and context setting
2. **Question Flow**: AI asks personalized questions based on CV
3. **Response Collection**: User types responses with STAR guidance
4. **Follow-up Questions**: AI generates contextual follow-ups
5. **Progress Tracking**: Visual progress through 15-question session
6. **Session Completion**: Automatic transition to feedback page
