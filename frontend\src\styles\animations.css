/* Global Animations for AI Interview Coach */

/* Fade In */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Fade In Right */
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade In Left */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade In Up */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade In Down */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Bounce */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Slide In Up */
@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Slide In Down */
@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Glow */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(79, 140, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(79, 140, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(79, 140, 255, 0.5);
  }
}

/* Rotate */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Scale */
@keyframes scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Shake */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Typing */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

/* Blink */
@keyframes blink {
  50% {
    opacity: 0;
  }
}

/* Animation Classes */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-fadeInRight {
  animation: fadeInRight 0.5s ease-out forwards;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.5s ease-out forwards;
}

.animate-fadeInUp {
  animation: fadeInUp 0.5s ease-out forwards;
}

.animate-fadeInDown {
  animation: fadeInDown 0.5s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite ease-in-out;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease-out forwards;
}

.animate-slideInDown {
  animation: slideInDown 0.5s ease-out forwards;
}

.animate-glow {
  animation: glow 2s infinite;
}

.animate-rotate {
  animation: rotate 2s linear infinite;
}

.animate-scale {
  animation: scale 2s infinite;
}

.animate-shake {
  animation: shake 0.5s;
}

/* Animation Delays */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

.delay-1000 {
  animation-delay: 1000ms;
}
