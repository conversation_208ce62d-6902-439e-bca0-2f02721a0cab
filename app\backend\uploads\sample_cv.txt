<PERSON>
123 Main Street, Anytown, USA
Phone: (*************
Email: <EMAIL>
LinkedIn: linkedin.com/in/john-smith

SUMMARY
Experienced software engineer with 5+ years of experience in full-stack development. Proficient in Python, JavaScript, and React. Strong problem-solving skills and a passion for creating efficient, scalable applications.

SKILLS
Programming Languages: Python, JavaScript, TypeScript, Java, C++
Web Technologies: HTML5, CSS3, React, Angular, Node.js
Databases: MongoDB, MySQL, PostgreSQL
Tools & Platforms: Git, Docker, AWS, Azure, Jenkins
Methodologies: Agile, Scrum, Test-Driven Development

EXPERIENCE
Senior Software Engineer
ABC Tech Solutions, New York, NY
January 2020 - Present
• Developed and maintained multiple web applications using React and Node.js
• Implemented RESTful APIs using Python Flask and Django
• Improved application performance by 40% through code optimization
• Led a team of 5 developers for a major client project
• Collaborated with product managers to define and implement new features

Software Developer
XYZ Software Inc., Boston, MA
June 2017 - December 2019
• Built responsive web applications using Angular and TypeScript
• Created and maintained database schemas using PostgreSQL
• Implemented automated testing using <PERSON><PERSON> and <PERSON><PERSON><PERSON>
• Participated in code reviews and mentored junior developers
• Contributed to open-source projects related to data visualization

Junior Developer
Tech Startups LLC, San Francisco, CA
January 2016 - May 2017
• Assisted in developing front-end components using React
• Fixed bugs and implemented minor features in existing applications
• Participated in daily stand-up meetings and sprint planning
• Learned and applied best practices for web development

EDUCATION
Master of Science in Computer Science
Stanford University, Stanford, CA
September 2014 - May 2016
• GPA: 3.8/4.0
• Thesis: "Efficient Algorithms for Large-Scale Data Processing"
• Relevant coursework: Advanced Algorithms, Machine Learning, Distributed Systems

Bachelor of Science in Computer Engineering
MIT, Cambridge, MA
September 2010 - May 2014
• GPA: 3.7/4.0
• Dean's List: All semesters
• Relevant coursework: Data Structures, Computer Architecture, Operating Systems

PROJECTS
E-commerce Platform (2020)
• Developed a full-stack e-commerce platform using MERN stack
• Implemented secure payment processing with Stripe API
• Created an admin dashboard for inventory management

Data Visualization Tool (2019)
• Built an interactive data visualization tool using D3.js
• Implemented data filtering and transformation features
• Optimized for performance with large datasets

CERTIFICATIONS
• AWS Certified Solutions Architect (2021)
• Microsoft Certified: Azure Developer Associate (2020)
• Certified Scrum Master (2019)

LANGUAGES
• English (Native)
• Spanish (Intermediate)
• French (Basic)

INTERESTS
Machine Learning, Open Source Development, Hiking, Photography
