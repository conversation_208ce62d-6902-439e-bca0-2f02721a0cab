Research:
Demonstrated good understanding of research methodologies. Applied the method appropriately and correctly and is clearly evidenced in the report. Demonstrated the ability to evaluate their own use of the method.

Excellent:
Demonstrates an exceptional understanding of research methodologies with detailed and accurate explanations. Applies the method appropriately and correctly with clear, detailed
evidence in the report. Demonstrates a thorough and insightful evaluation of their own use of the method, showing deep reflection and understanding.


Literature Review:
A complete report of the background study which shows very good understand of the topic. The report has clearly distinguished between facts, speculations, and opinions.
quality and quantity of the selected references to support argument. There evidence that the researcher has read, analyzed, and reflected on the knowledge relevant to the topic.

Excellent:
Comprehensive report demonstrating deep and thorough understanding of the topic. Covers all relevant aspects and subtopics with clarity and detail. Clear and consistent distinction between facts, speculations, and opinions throughout the report. Effective use of evidence to support each type. References are current and from credible sources.
Insights and arguments are well-developed and original.


Evidence Used:
Design models demonstrated in the report. Clarity of evidence of progress towards project aims and objectives e.g. prototype, demonstration, evaluation instrument, software coding, UML models. EITHER distinguishes between different types of evidence collected OR provides appropriate evidence of software analysis / design / development / quality 
/ testing.

Excellent:
Evidence is exceptionally clear, comprehensive, and directly linked to project aims and objectives. Each piece of evidence directly supports the progress towards achieving 
project aims and objectives. Thorough and highly relevant evidence of software analysis, design, development, quality or testing with no significant gaps.


Result Analysis:
Conceptual grasp of topic, clear arguments, indicates limits of method chosen for investigation or software development. Review critically the outcome and progress of your work, suggests further work.

Excellent: 
Deep understanding of the topic; arguments are exceptionally clear, relevant, insightful and original. Thoroughly and insightfully explains all significant limits or weaknesses with excellent critical analysis. Provides an in-depth and insightful review of the outcome and progress; suggests further work that is highly relevant, innovative, and well-justified.


Conclusion:
Quality of conclusions based on the evidence collected.

Excellent:
Conclusions are comprehensively supported by the evidence presented. There is a clear, logical connection between evidence and the conclusions drawn. Analysis is deep, insightful, and considers multiple perspectives and implications. Conclusions are stated clearly and precisely, leaving no room for ambiguity.


References:
Appropriate references listed with the correct in-text citation.

Excellent:
All references are highly relevant, credible, and up-to-date. The reference list is complete, correctly formatted, and alphabetized. There are no errors or omissions in citation details (authors' names, publication dates, titles, etc.).


Logbook Sem2
Evidence of engagement with the supervisor.

Excellent:
Communicates very frequently, providing comprehensive updates and actively seeking constructive feedback. Proactively seeks feedback, discusses progress, and regularly proposes innovative ideas or solutions. Responds immediately to messages or instructions, demonstrating high responsiveness and engagement.
